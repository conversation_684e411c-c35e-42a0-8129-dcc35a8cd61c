<svg width="500" height="400" viewBox="0 0 500 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background gradient -->
  <defs>
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F9FAFB;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#EEF2FF;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gold-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#D4AF37;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#B8941F;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="red-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B0000;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#DC143C;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background removed for transparency -->
  
  <!-- AI Brain representation -->
  <circle cx="250" cy="150" r="80" fill="url(#gold-gradient)" opacity="0.2"/>
  <circle cx="250" cy="150" r="60" fill="url(#gold-gradient)" opacity="0.4"/>
  <circle cx="250" cy="150" r="40" fill="url(#gold-gradient)"/>
  
  <!-- Neural network lines -->
  <g stroke="#D4AF37" stroke-width="2" opacity="0.6">
    <line x1="200" y1="120" x2="300" y2="180"/>
    <line x1="220" y1="100" x2="280" y2="200"/>
    <line x1="180" y1="140" x2="320" y2="160"/>
    <line x1="210" y1="180" x2="290" y2="120"/>
  </g>
  
  <!-- Data nodes -->
  <circle cx="200" cy="120" r="8" fill="url(#red-gradient)"/>
  <circle cx="300" cy="180" r="8" fill="url(#red-gradient)"/>
  <circle cx="220" cy="100" r="6" fill="url(#red-gradient)"/>
  <circle cx="280" cy="200" r="6" fill="url(#red-gradient)"/>
  <circle cx="180" cy="140" r="6" fill="url(#red-gradient)"/>
  <circle cx="320" cy="160" r="6" fill="url(#red-gradient)"/>
  
  <!-- Bazi symbols (八字) -->
  <g transform="translate(220, 130)">
    <text x="0" y="0" font-family="serif" font-size="24" fill="#1E3A8A" font-weight="bold">八</text>
    <text x="20" y="0" font-family="serif" font-size="24" fill="#1E3A8A" font-weight="bold">字</text>
  </g>
  
  <!-- Floating elements -->
  <g opacity="0.7">
    <circle cx="100" cy="80" r="4" fill="#D4AF37">
      <animate attributeName="cy" values="80;70;80" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="400" cy="100" r="6" fill="#8B0000">
      <animate attributeName="cy" values="100;90;100" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="80" cy="200" r="5" fill="#1E3A8A">
      <animate attributeName="cy" values="200;190;200" dur="3.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="420" cy="220" r="4" fill="#D4AF37">
      <animate attributeName="cy" values="220;210;220" dur="2.8s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Report/Document representation -->
  <rect x="150" y="280" width="200" height="100" rx="10" fill="white" stroke="#E5E7EB" stroke-width="2"/>
  <rect x="170" y="300" width="160" height="8" rx="4" fill="#D4AF37"/>
  <rect x="170" y="320" width="120" height="6" rx="3" fill="#E5E7EB"/>
  <rect x="170" y="335" width="140" height="6" rx="3" fill="#E5E7EB"/>
  <rect x="170" y="350" width="100" height="6" rx="3" fill="#E5E7EB"/>
  
  <!-- Mobile device outline -->
  <rect x="320" y="260" width="60" height="100" rx="15" fill="white" stroke="#D4AF37" stroke-width="3"/>
  <rect x="330" y="275" width="40" height="60" rx="5" fill="#F9FAFB"/>
  <circle cx="350" cy="350" r="8" fill="#D4AF37"/>
  
  <!-- Decorative elements -->
  <g transform="translate(50, 300)" opacity="0.5">
    <polygon points="0,20 10,0 20,20 10,40" fill="#8B0000"/>
  </g>
  <g transform="translate(430, 50)" opacity="0.5">
    <polygon points="0,15 8,0 16,15 8,30" fill="#1E3A8A"/>
  </g>
</svg>
