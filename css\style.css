/* CSS Custom Properties */
:root {
  /* Colors - <PERSON> inspired palette */
  --primary-color: #D4AF37; /* Gold */
  --primary-dark: #B8941F;
  --secondary-color: #8B0000; /* Deep Red */
  --accent-color: #1E3A8A; /* Deep Blue */
  --text-primary: #1F2937;
  --text-secondary: #6B7280;
  --text-light: #9CA3AF;
  --background: #FFFFFF;
  --background-light: #F9FAFB;
  --background-dark: #111827;
  --border-color: #E5E7EB;
  --shadow-light: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-large: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  
  /* Typography */
  --font-primary: 'Inter', 'Noto Sans', sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  
  /* Layout */
  --container-max-width: 1200px;
  --border-radius: 0.5rem;
  --border-radius-lg: 1rem;
  
  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

a {
  text-decoration: none;
  color: inherit;
  transition: color var(--transition-fast);
}

ul {
  list-style: none;
}

/* Container */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  border: 2px solid transparent;
  border-radius: var(--border-radius);
  font-weight: 600;
  font-size: var(--font-size-sm);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  text-decoration: none;
  white-space: nowrap;
}

.btn--primary {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.btn--primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.btn--secondary {
  background-color: transparent;
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn--secondary:hover {
  background-color: var(--primary-color);
  color: white;
}

.btn--outline {
  background-color: transparent;
  color: var(--text-primary);
  border-color: var(--border-color);
}

.btn--outline:hover {
  background-color: var(--text-primary);
  color: white;
  border-color: var(--text-primary);
}

.btn--large {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-base);
}

/* Section Styles */
.section__header {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
}

.section__title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.section__subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* Header */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  z-index: 1000;
  transition: all var(--transition-normal);
}

.nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) 0;
}

.nav__brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.nav__logo {
  width: 40px;
  height: 40px;
}

.nav__title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--primary-color);
}

.nav__menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
}

.nav__link {
  font-weight: 500;
  color: var(--text-primary);
  transition: color var(--transition-fast);
}

.nav__link:hover {
  color: var(--primary-color);
}

.nav__toggle {
  display: none;
  flex-direction: column;
  gap: 4px;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-sm);
}

.nav__toggle span {
  width: 25px;
  height: 3px;
  background-color: var(--text-primary);
  transition: all var(--transition-normal);
}

/* Hero Section */
.hero {
  padding: calc(80px + var(--spacing-3xl)) 0 var(--spacing-3xl);
  background: linear-gradient(135deg, #F9FAFB 0%, #EEF2FF 100%);
  overflow: hidden;
}

.hero__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  align-items: center;
}

.hero__title {
  font-size: var(--font-size-5xl);
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
}

.hero__highlight {
  color: var(--primary-color);
  position: relative;
}

.hero__highlight::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: 2px;
}

.hero__subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  line-height: 1.7;
}

.hero__cta {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-2xl);
}

.hero__stats {
  display: flex;
  gap: var(--spacing-xl);
}

.stat {
  text-align: center;
}

.stat__number {
  display: block;
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--primary-color);
}

.stat__label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.hero__image {
  position: relative;
}

.hero__img {
  width: 100%;
  height: auto;
  filter: drop-shadow(var(--shadow-large));
}

/* About Section */
.about {
  padding: var(--spacing-3xl) 0;
  background-color: var(--background);
}

.about__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  align-items: start;
}

.about__text h3 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.about__text p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  line-height: 1.7;
}

.about__features {
  display: grid;
  gap: var(--spacing-lg);
}

.feature {
  display: flex;
  gap: var(--spacing-md);
  align-items: flex-start;
}

.feature__icon {
  font-size: var(--font-size-2xl);
  flex-shrink: 0;
}

.feature h4 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.feature p {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

/* Features Section */
.features {
  padding: var(--spacing-3xl) 0;
  background-color: var(--background-light);
}

.features__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-xl);
}

.feature-card {
  background-color: var(--background);
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  transition: all var(--transition-normal);
  border: 1px solid var(--border-color);
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-large);
}

.feature-card__icon {
  width: 60px;
  height: 60px;
  margin-bottom: var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--border-radius);
}

.feature-card__icon img {
  width: 30px;
  height: 30px;
  filter: brightness(0) invert(1);
}

.feature-card__title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.feature-card__description {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Testimonials Section */
.testimonials {
  padding: var(--spacing-3xl) 0;
  background-color: var(--background);
}

.testimonials__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-xl);
}

.testimonial {
  background-color: var(--background-light);
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
}

.testimonial__text {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
  line-height: 1.6;
  font-style: italic;
}

.testimonial__author {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.testimonial__avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
}

.testimonial__name {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.testimonial__role {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.testimonial__rating {
  display: flex;
  gap: var(--spacing-xs);
}

.star {
  color: #FCD34D;
  font-size: var(--font-size-lg);
}

/* Pricing Section */
.pricing {
  padding: var(--spacing-3xl) 0;
  background-color: var(--background-light);
}

.pricing__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
  max-width: 1000px;
  margin: 0 auto;
}

.pricing-card {
  background-color: var(--background);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  position: relative;
  transition: all var(--transition-normal);
}

.pricing-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-large);
}

.pricing-card--featured {
  border-color: var(--primary-color);
  transform: scale(1.05);
}

.pricing-card--featured:hover {
  transform: scale(1.05) translateY(-5px);
}

.pricing-card__badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--primary-color);
  color: white;
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  font-weight: 600;
}

.pricing-card__header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.pricing-card__title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.pricing-card__price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}

.price {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--primary-color);
}

.period {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
}

.pricing-card__credits {
  text-align: center;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.pricing-card__per-credit {
  text-align: center;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
}

.pricing-card__description {
  text-align: center;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  font-style: italic;
}

.pricing-card__features {
  margin-bottom: var(--spacing-xl);
}

.pricing-card__features li {
  padding: var(--spacing-sm) 0;
  color: var(--text-secondary);
  position: relative;
  padding-left: var(--spacing-lg);
}

.pricing-card__features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--primary-color);
  font-weight: bold;
}

.pricing-card__cta {
  width: 100%;
  justify-content: center;
}

/* CTA Section */
.cta {
  padding: var(--spacing-3xl) 0;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  text-align: center;
}

.cta__title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  margin-bottom: var(--spacing-md);
}

.cta__subtitle {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-xl);
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta__button {
  background-color: white;
  color: var(--primary-color);
  border-color: white;
  margin-bottom: var(--spacing-md);
}

.cta__button:hover {
  background-color: var(--background-light);
  transform: translateY(-2px);
}

.cta__note {
  font-size: var(--font-size-sm);
  opacity: 0.8;
}

/* Footer */
.footer {
  background-color: var(--background-dark);
  color: white;
  padding: var(--spacing-3xl) 0 var(--spacing-xl);
}

.footer__content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--spacing-3xl);
  margin-bottom: var(--spacing-xl);
}

.footer__brand {
  max-width: 300px;
}

.footer__logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.footer__logo img {
  width: 40px;
  height: 40px;
}

.footer__title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--primary-color);
}

.footer__description {
  color: #9CA3AF;
  margin-bottom: var(--spacing-lg);
  line-height: 1.6;
}

.footer__social {
  display: flex;
  gap: var(--spacing-md);
}

.social-link {
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-normal);
}

.social-link:hover {
  background-color: var(--primary-color);
  transform: translateY(-2px);
}

.social-link img {
  width: 20px;
  height: 20px;
  filter: brightness(0) invert(1);
}

.footer__links {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-xl);
}

.footer__heading {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  color: white;
}

.footer__list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.footer__link {
  color: #9CA3AF;
  font-size: var(--font-size-sm);
  transition: color var(--transition-fast);
}

.footer__link:hover {
  color: var(--primary-color);
}

.footer__bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--spacing-xl);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer__copyright {
  color: #9CA3AF;
  font-size: var(--font-size-sm);
}

.footer__contact {
  display: flex;
  gap: var(--spacing-lg);
  font-size: var(--font-size-sm);
  color: #9CA3AF;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.text-primary {
  color: var(--primary-color);
}

.text-secondary {
  color: var(--text-secondary);
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }
